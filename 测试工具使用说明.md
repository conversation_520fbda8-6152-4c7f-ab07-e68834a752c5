# 三交易所WebSocket调试测试工具使用说明

## 🎯 工具概述

这套工具专门用于测试你提到的三个交易所WebSocket连接问题：
- **数据流阻塞** (silent_duration_seconds > 102.519)
- **时间戳不同步** (time_diff_ms > 8980) 
- **数据新鲜度检查失败** (timestamp_age_ms > 91569)

## 🚀 快速开始

### 方法1：一键运行（推荐）

**Windows用户：**
```cmd
run_test.bat
```

**Linux/Mac用户：**
```bash
./run_test.sh
```

### 方法2：直接运行Python

**快速测试（推荐新手）：**
```bash
python3 quick_test.py
```

**完整调试测试：**
```bash
python3 debug_websocket_test.py
```

## 📁 文件说明

| 文件名 | 用途 | 推荐场景 |
|--------|------|----------|
| `quick_test.py` | 快速验证连接 | 🌟 首次使用 |
| `debug_websocket_test.py` | 完整问题检测 | 🔍 深度调试 |
| `simple_websocket_test.py` | 基础连接测试 | ✅ 快速验证 |
| `issue_detector.py` | 问题模拟器 | 🧪 逻辑验证 |
| `run_tests.py` | 菜单界面 | 📋 多选择 |

## ⏱️ 测试参数

- **测试时长：** 2分钟（120秒）
- **监控频率：** 每秒检查
- **静默阈值：** 30秒无数据 = 阻塞
- **时间戳阈值：** 800ms差异 = 不同步
- **新鲜度阈值：** 5000ms年龄 = 过期

## 📊 结果解读

### ✅ 正常输出
```
[GATE] ✅ WebSocket已连接
[OKX] ✅ WebSocket已连接
[BYBIT] ✅ WebSocket已连接
📊 [GATE] 🟢 连接 | 消息: 245 | 错误: 0
🎉 所有测试通过！
```

### ⚠️ 问题输出
```
🚨 [SILENT] WARNING - [okx] 检测到数据流阻塞 | {'silent_duration_seconds': 35.123}
⏰ [PERF] DEBUG - 价格数据时间戳不同步 | {'time_diff_ms': 8980}
🕐 [PERF] DEBUG - 数据新鲜度检查失败 | {'timestamp_age_ms': 91569}
```

## 🔧 故障排除

### 常见错误及解决方案

**1. 模块导入错误**
```
ModuleNotFoundError: No module named 'websocket'
```
**解决：** 确保在项目根目录运行，包含websocket文件夹

**2. 连接超时**
```
[GATE] ❌ WebSocket连接失败
```
**解决：** 检查网络连接，可能需要VPN

**3. Python版本问题**
```
SyntaxError: invalid syntax
```
**解决：** 使用Python 3.7+版本

## 📈 测试建议

### 🥇 推荐测试流程

1. **第一步：快速测试**
   ```bash
   python3 quick_test.py
   ```
   验证基本连接是否正常

2. **第二步：完整测试**
   ```bash
   python3 debug_websocket_test.py
   ```
   深度检测所有问题

3. **第三步：分析报告**
   查看生成的JSON报告文件

### 🎯 最佳实践

- **测试时机：** 选择交易活跃时段（北京时间9:00-24:00）
- **网络环境：** 确保稳定的网络连接
- **运行环境：** 建议在Linux服务器上测试
- **测试频率：** 每天测试1-2次，持续监控

## 📄 输出文件

测试完成后会生成以下文件：

| 文件类型 | 文件名格式 | 内容 |
|----------|------------|------|
| 调试日志 | `debug_websocket_[时间戳].log` | 详细运行日志 |
| 问题日志 | `issue_detection_[时间戳].log` | 问题检测日志 |
| JSON报告 | `websocket_debug_report_[时间戳].json` | 结构化测试结果 |

## 🎪 高级用法

### 自定义测试参数

编辑脚本中的参数：
```python
self.test_duration = 120  # 测试时长（秒）
self.silent_threshold = 30  # 静默阈值（秒）
self.timestamp_sync_threshold = 800  # 时间戳阈值（毫秒）
self.freshness_threshold = 5000  # 新鲜度阈值（毫秒）
```

### 添加更多交易对

修改测试交易对：
```python
self.test_symbols = ["BTC-USDT", "ETH-USDT", "SOL-USDT"]  # 添加更多
```

## 🆘 技术支持

如果遇到问题，请提供：

1. **完整错误日志**
2. **Python版本信息** (`python3 --version`)
3. **操作系统信息**
4. **网络连接状态**
5. **项目目录结构** (`ls -la`)

## 📞 联系方式

- 提供详细的错误信息和日志
- 说明具体的测试场景和期望结果
- 包含系统环境信息

---

## 🎉 开始测试

现在你可以开始测试了！推荐从快速测试开始：

```bash
# Windows
run_test.bat

# Linux/Mac  
./run_test.sh

# 或直接运行
python3 quick_test.py
```

祝测试顺利！🚀
