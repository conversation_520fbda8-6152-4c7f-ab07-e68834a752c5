# 三交易所WebSocket调试测试工具

## 概述

这套工具专门用于测试和调试三个交易所（Gate.io、OKX、Bybit）的WebSocket连接稳定性和时间戳同步问题。

## 检测的问题

基于你提供的日志，工具专门检测以下问题：

### 1. 数据流阻塞
```
2025-08-02 08:41:07,821 [SILENT] WARNING - [okx] 检测到数据流阻塞 | {'silent_duration_seconds': 102.519, 'last_update_time': 1754116765302}
2025-08-02 08:41:07,824 [SILENT] WARNING - [gate] 检测到数据流阻塞 | {'silent_duration_seconds': 66.98, 'last_update_time': 1754116800844}
```

### 2. 时间戳不同步
```
2025-08-02 08:40:57,359 [PERF] DEBUG - 价格数据时间戳不同步，丢弃套利机会 | {'combo_name': 'gate_spot_okx_futures', 'spot_exchange': 'gate', 'futures_exchange': 'okx', 'time_diff_ms': 8980, 'max_diff_ms': 800, 'spot_timestamp': 1754116847740, 'futures_timestamp': 1754116856720}
```

### 3. 数据新鲜度检查失败
```
2025-08-02 08:40:57,360 [PERF] DEBUG - 数据新鲜度检查失败，丢弃过期时间戳 | {'exchange': 'gate', 'timestamp_age_ms': 91569, 'max_age_ms': 5000, 'extraction_source': 'gate_t_field', 'discarded_timestamp': 1754116765791}
```

## 测试脚本说明

### 1. debug_websocket_test.py - 完整调试测试
**功能：**
- 同时连接三个交易所的WebSocket
- 实时监控数据流状态
- 检测静默期间（>30秒无数据）
- 验证时间戳同步（阈值800ms）
- 检查数据新鲜度（阈值5000ms）
- 生成详细的JSON报告

**适用场景：** 深度调试和问题分析

### 2. simple_websocket_test.py - 简化连接测试
**功能：**
- 快速验证三个交易所的基本连接
- 统计消息数量和错误
- 简化的状态报告

**适用场景：** 快速验证连接是否正常

### 3. issue_detector.py - 问题检测器
**功能：**
- 专门检测你提到的具体问题
- 模拟问题场景进行测试
- 验证检测逻辑的准确性
- 使用与你的日志相同的格式

**适用场景：** 验证问题检测逻辑

### 4. run_tests.py - 测试运行器
**功能：**
- 提供友好的菜单界面
- 方便选择和运行不同的测试
- 检查依赖文件

## 使用方法

### 方法1：使用运行器（推荐）
```bash
python3 run_tests.py
```
然后根据菜单选择要运行的测试。

### 方法2：直接运行测试
```bash
# 完整调试测试（2分钟）
python3 debug_websocket_test.py

# 简化连接测试（2分钟）
python3 simple_websocket_test.py

# 问题检测器（2分钟）
python3 issue_detector.py
```

## 测试参数

### 时间设置
- **测试时长：** 2分钟（120秒）
- **监控频率：** 每秒检查一次
- **报告间隔：** 每30秒报告一次状态

### 阈值设置
- **静默阈值：** 30秒（超过此时间无数据认为阻塞）
- **时间戳同步阈值：** 800ms（超过此差值认为不同步）
- **数据新鲜度阈值：** 5000ms（超过此年龄认为过期）

### 测试交易对
- **Gate.io & OKX：** BTC-USDT, ETH-USDT
- **Bybit：** BTCUSDT, ETHUSDT

## 输出文件

### 日志文件
- `debug_websocket_[timestamp].log` - 详细调试日志
- `issue_detection_[timestamp].log` - 问题检测日志

### 报告文件
- `websocket_debug_report_[timestamp].json` - 详细的JSON格式报告

## 报告解读

### 正常输出示例
```
[GATE] ✅ WebSocket已连接
[OKX] ✅ WebSocket已连接  
[BYBIT] ✅ WebSocket已连接
📊 测试进度: 60.0s / 120s
[GATE] 🟢 连接 | 消息: 245 | 订单簿: 240 | 错误: 0 | 最后消息: 0.5s前
```

### 问题输出示例
```
[SILENT] WARNING - [okx] 检测到数据流阻塞 | {'silent_duration_seconds': 35.123, 'last_update_time': 1754116765302}
[PERF] DEBUG - 价格数据时间戳不同步，丢弃套利机会 | {'time_diff_ms': 8980, 'max_diff_ms': 800}
```

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ModuleNotFoundError: No module named 'websocket'
   ```
   **解决：** 确保在项目根目录运行，包含websocket模块

2. **连接失败**
   ```
   [GATE] ❌ WebSocket连接失败
   ```
   **解决：** 检查网络连接和防火墙设置

3. **时间同步失败**
   ```
   ⚠️ 时间同步失败: Connection timeout
   ```
   **解决：** 检查网络连接，可能需要代理

### 调试建议

1. **先运行简化测试** 验证基本连接
2. **再运行完整测试** 进行深度分析
3. **查看日志文件** 获取详细信息
4. **分析JSON报告** 了解具体问题

## 技术细节

### 监控机制
- 使用异步任务并发监控三个交易所
- 实时记录消息时间戳和数据质量
- 自动检测连接状态变化

### 时间戳处理
- 集成项目的统一时间戳处理器
- 支持多种时间戳格式转换
- 实现跨交易所时间同步

### 数据验证
- 验证订单簿数据完整性
- 检查时间戳新鲜度
- 监控数据流连续性

## 注意事项

1. **测试时长固定为2分钟**，足够检测大部分问题
2. **需要稳定的网络连接**，避免误报网络问题
3. **建议在交易活跃时段测试**，数据更新更频繁
4. **测试结果仅供参考**，实际部署需要更长时间验证

## 联系支持

如果遇到问题或需要自定义测试参数，请提供：
1. 完整的错误日志
2. 运行环境信息
3. 网络连接状态
4. 具体的测试需求
