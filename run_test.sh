#!/bin/bash

echo "🚀 三交易所WebSocket测试工具"
echo "================================"
echo

echo "📍 当前目录: $(pwd)"
echo

echo "🔍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到Python3，请安装Python 3.7+"
    exit 1
fi

python3 --version
echo

echo "🎯 选择测试类型:"
echo "1. 快速测试 (推荐)"
echo "2. 完整调试测试"
echo "3. 简化连接测试"
echo "4. 问题检测器"
echo "5. 测试菜单"
echo

read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo "🚀 运行快速测试..."
        python3 quick_test.py
        ;;
    2)
        echo "🚀 运行完整调试测试..."
        python3 debug_websocket_test.py
        ;;
    3)
        echo "🚀 运行简化连接测试..."
        python3 simple_websocket_test.py
        ;;
    4)
        echo "🚀 运行问题检测器..."
        python3 issue_detector.py
        ;;
    5)
        echo "🚀 运行测试菜单..."
        python3 run_tests.py
        ;;
    *)
        echo "❌ 无效选择"
        ;;
esac

echo
echo "测试完成！"
read -p "按回车键退出..."
