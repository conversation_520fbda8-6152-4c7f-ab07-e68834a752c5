2025-08-02 08:55:16,551 [DEBUG] [asyncio] Using selector: EpollSelector
2025-08-02 08:55:16,551 [INFO] [__main__] 🔬 开始OKX WebSocket综合诊断
2025-08-02 08:55:16,551 [INFO] [__main__] 🕐 开始时间戳同步诊断
2025-08-02 08:55:16,898 [INFO] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间同步成功，偏移: -57ms
2025-08-02 08:55:16,899 [INFO] [__main__] 时间同步状态: {
  "exchange": "okx",
  "time_synced": true,
  "time_offset_ms": -57,
  "last_sync_time": 1754117716.5519373,
  "sync_age_seconds": 0.3477060794830322,
  "sync_retry_count": 0,
  "offset_status": "NORMAL",
  "sync_interval_seconds": 20,
  "max_offset_threshold": 1000
}
2025-08-02 08:55:16,899 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间戳新鲜: 1754117716899 (来源:okx_api_ts_field, 年龄:0.0ms)
2025-08-02 08:55:16,900 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx使用服务器时间戳: 1754117716899 (年龄1.0ms)
2025-08-02 08:55:16,900 [INFO] [__main__] 时间戳测试 1: 生成=1754117716899, 当前=1754117716900, 差异=1ms
2025-08-02 08:55:17,000 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间戳新鲜: 1754117717000 (来源:okx_api_ts_field, 年龄:0.0ms)
2025-08-02 08:55:17,001 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx使用服务器时间戳: 1754117717000 (年龄1.0ms)
2025-08-02 08:55:17,001 [INFO] [__main__] 时间戳测试 2: 生成=1754117717000, 当前=1754117717001, 差异=1ms
2025-08-02 08:55:17,101 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间戳新鲜: 1754117717101 (来源:okx_api_ts_field, 年龄:0.0ms)
2025-08-02 08:55:17,102 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx使用服务器时间戳: 1754117717101 (年龄1.0ms)
2025-08-02 08:55:17,102 [INFO] [__main__] 时间戳测试 3: 生成=1754117717101, 当前=1754117717102, 差异=1ms
2025-08-02 08:55:17,202 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间戳新鲜: 1754117717202 (来源:okx_api_ts_field, 年龄:0.0ms)
2025-08-02 08:55:17,202 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx使用服务器时间戳: 1754117717202 (年龄0.0ms)
2025-08-02 08:55:17,202 [INFO] [__main__] 时间戳测试 4: 生成=1754117717202, 当前=1754117717202, 差异=0ms
2025-08-02 08:55:17,303 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间戳新鲜: 1754117717303 (来源:okx_api_ts_field, 年龄:0.0ms)
2025-08-02 08:55:17,303 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx使用服务器时间戳: 1754117717303 (年龄0.0ms)
2025-08-02 08:55:17,303 [INFO] [__main__] 时间戳测试 5: 生成=1754117717303, 当前=1754117717303, 差异=0ms
2025-08-02 08:55:17,404 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间戳新鲜: 1754117717404 (来源:okx_api_ts_field, 年龄:0.0ms)
2025-08-02 08:55:17,404 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx使用服务器时间戳: 1754117717404 (年龄0.0ms)
2025-08-02 08:55:17,404 [INFO] [__main__] 时间戳测试 6: 生成=1754117717404, 当前=1754117717404, 差异=0ms
2025-08-02 08:55:17,504 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间戳新鲜: 1754117717504 (来源:okx_api_ts_field, 年龄:0.0ms)
2025-08-02 08:55:17,505 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx使用服务器时间戳: 1754117717504 (年龄1.0ms)
2025-08-02 08:55:17,505 [INFO] [__main__] 时间戳测试 7: 生成=1754117717504, 当前=1754117717505, 差异=1ms
2025-08-02 08:55:17,606 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间戳新鲜: 1754117717605 (来源:okx_api_ts_field, 年龄:0.0ms)
2025-08-02 08:55:17,606 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx使用服务器时间戳: 1754117717605 (年龄1.0ms)
2025-08-02 08:55:17,606 [INFO] [__main__] 时间戳测试 8: 生成=1754117717605, 当前=1754117717606, 差异=1ms
2025-08-02 08:55:17,707 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间戳新鲜: 1754117717707 (来源:okx_api_ts_field, 年龄:0.0ms)
2025-08-02 08:55:17,708 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx使用服务器时间戳: 1754117717707 (年龄1.0ms)
2025-08-02 08:55:17,708 [INFO] [__main__] 时间戳测试 9: 生成=1754117717707, 当前=1754117717708, 差异=1ms
2025-08-02 08:55:17,808 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间戳新鲜: 1754117717808 (来源:okx_api_ts_field, 年龄:0.0ms)
2025-08-02 08:55:17,808 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx使用服务器时间戳: 1754117717808 (年龄0.0ms)
2025-08-02 08:55:17,808 [INFO] [__main__] 时间戳测试 10: 生成=1754117717808, 当前=1754117717808, 差异=0ms
2025-08-02 08:55:17,909 [INFO] [__main__] 🔌 开始WebSocket连接诊断
2025-08-02 08:55:17,911 [INFO] [websocket] [OKX] 初始化OKX WebSocket客户端
2025-08-02 08:55:17,911 [INFO] [websocket] [OKX] 初始化OKX现货WebSocket
2025-08-02 08:55:17,911 [ERROR] [__main__] ❌ WebSocket连接诊断失败: 'OKXWebSocketClient' object has no attribute 'set_data_handler'
2025-08-02 08:55:17,916 [INFO] [__main__] 🚫 开始数据流阻塞诊断
2025-08-02 08:55:17,917 [INFO] [__main__] 🔍 分析数据流阻塞原因
2025-08-02 08:55:17,917 [WARNING] [__main__] 🔍 阻塞原因 [HIGH]: WebSocket连接不稳定 - 发现 1 个连接问题
2025-08-02 08:55:17,917 [INFO] [__main__] 🔄 开始跨交易所时间戳一致性诊断
2025-08-02 08:55:18,075 [INFO] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ gate时间同步成功，偏移: -4ms
2025-08-02 08:55:18,076 [INFO] [__main__] gate 同步结果: True
2025-08-02 08:55:18,207 [INFO] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ bybit时间同步成功，偏移: -38ms
2025-08-02 08:55:18,207 [INFO] [__main__] bybit 同步结果: True
2025-08-02 08:55:18,346 [INFO] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间同步成功，偏移: -31ms
2025-08-02 08:55:18,346 [INFO] [__main__] okx 同步结果: True
2025-08-02 08:55:18,346 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🕐 gate使用统一时间基准: 1754117718346
2025-08-02 08:55:18,346 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ bybit时间戳新鲜: 1754117718346 (来源:bybit_ts_field, 年龄:0.0ms)
2025-08-02 08:55:18,346 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ bybit使用服务器时间戳: 1754117718346 (年龄0.0ms)
2025-08-02 08:55:18,346 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx时间戳新鲜: 1754117718346 (来源:okx_api_ts_field, 年龄:0.0ms)
2025-08-02 08:55:18,346 [DEBUG] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ✅ okx使用服务器时间戳: 1754117718346 (年龄0.0ms)
2025-08-02 08:55:18,346 [INFO] [__main__] 交易所时间戳: {'gate': 1754117718346, 'bybit': 1754117718346, 'okx': 1754117718346}
2025-08-02 08:55:18,347 [INFO] [__main__] 最大时间差: 0ms
2025-08-02 08:55:18,347 [INFO] [__main__] 📋 生成诊断报告
2025-08-02 08:55:18,347 [INFO] [__main__] 📄 诊断报告已保存: /root/myproject/123/67D okx 还是有问题/123/diagnostic_results/okx_diagnosis_report_1754117718.json
2025-08-02 08:55:18,347 [INFO] [__main__] ================================================================================
2025-08-02 08:55:18,347 [INFO] [__main__] 🔬 OKX WebSocket 诊断报告总结
2025-08-02 08:55:18,348 [INFO] [__main__] ================================================================================
2025-08-02 08:55:18,348 [INFO] [__main__] 总问题数: 1
2025-08-02 08:55:18,348 [INFO] [__main__] 严重问题数: 0
2025-08-02 08:55:18,348 [INFO] [__main__] 时间戳同步健康: GOOD
2025-08-02 08:55:18,348 [INFO] [__main__] 数据流健康: GOOD
2025-08-02 08:55:18,348 [INFO] [__main__] 数据接收率: 0.00 条/秒
2025-08-02 08:55:18,348 [INFO] [__main__] 时间戳错误率: 0.00%
2025-08-02 08:55:18,348 [INFO] [__main__] 最大时间差: 0ms
2025-08-02 08:55:18,348 [INFO] [__main__] ================================================================================
