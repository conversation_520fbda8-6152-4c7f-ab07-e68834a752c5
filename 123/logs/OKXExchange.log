2025-08-02 09:32:08 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-02 09:32:08 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-02 09:32:08 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-02 09:32:08 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-02 09:32:08.539 [INFO] [exchanges.okx_exchange] 🚨 OKX API限制优化为2次/秒，解决WebSocket阻塞问题
2025-08-02 09:32:08.539 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-08-02 09:32:08 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-02 09:32:08 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-02 09:32:08 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-02 09:32:08 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-02 09:32:08.988 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 09:32:09.316 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 09:32:09.316 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 09:32:09.656 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 09:32:09.656 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 09:32:09 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-08-02 09:32:09.656 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 09:32:09.979 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 09:32:09.980 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 09:32:10.318 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 09:32:10.318 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 09:32:10 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-08-02 09:32:10.318 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 09:32:10.658 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 09:32:10.659 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 09:32:10.981 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 09:32:10.982 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 09:32:10 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-08-02 09:32:10.982 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 09:32:11.307 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 09:32:11.307 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 09:32:11.629 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 09:32:11.630 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 09:32:11 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-08-02 09:32:11 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-02 09:32:11.966 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 09:32:13.325 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 09:32:14.822 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 09:32:15.480 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 09:32:16.057 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 09:32:16.380 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 09:32:16.951 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 09:32:17.533 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 09:32:37.393 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 09:32:37.393 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 09:32:38.048 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 09:32:38.049 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 09:32:38.715 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 09:32:38.715 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 09:32:39.370 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 09:32:39.370 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 09:32:40.039 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 09:32:40.040 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 09:32:40.700 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 09:32:40.700 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
