2025-08-02 08:35:54 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-02 08:35:54 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-02 08:35:54 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-02 08:35:54 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-02 08:35:54.230 [INFO] [exchanges.okx_exchange] 🚨 OKX API限制优化为2次/秒，解决WebSocket阻塞问题
2025-08-02 08:35:54.230 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-08-02 08:35:54 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-02 08:35:54 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-02 08:35:54 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-02 08:35:54 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-02 08:35:54.694 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:35:55.023 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:35:55.023 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:35:55.417 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:35:55.417 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:35:55 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-08-02 08:35:55.417 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:35:55.679 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:35:55.679 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:35:56.004 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:35:56.005 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:35:56 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-08-02 08:35:56.005 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:35:56.367 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:35:56.367 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:35:56.676 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:35:56.677 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:35:56 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-08-02 08:35:56.677 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:35:56.994 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:35:56.994 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:35:57.346 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:35:57.346 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:35:57 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-08-02 08:35:57 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-02 08:35:57.653 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 08:35:59.030 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 08:36:00.540 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 08:36:01.200 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 08:36:01.778 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 08:36:02.116 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 08:36:02.708 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 08:36:03.285 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 08:36:23.110 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:36:23.110 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:36:23.774 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:36:23.774 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:36:24.423 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:36:24.423 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:36:25.097 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:36:25.097 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:36:25.752 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:36:25.752 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:36:26.426 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:36:26.426 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:37:08.623 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 08:37:08.965 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 08:37:08.981 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-08-02 08:37:08.982 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 08:37:08.983 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 08:37:11.394 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-02 08:37:11.722 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-02 08:37:11.723 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-08-02 08:37:11.733 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-08-02 08:37:11.752 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-02 08:37:12.754 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:37:13.088 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:37:13.089 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:37:13.089 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:37:13.089 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:37:13.089 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:37:13.089 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:37:13.089 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:37:13.089 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:37:13.089 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 08:37:13.167 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:37:13.167 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:37:13.501 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:37:13.501 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:37:13.510 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:37:13.510 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:37:13.511 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:37:13.512 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:37:13.519 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:37:13.520 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:37:13.521 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 08:37:13.521 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 08:37:13.521 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 08:37:13.521 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 08:37:13.521 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 08:37:13.522 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 08:37:13.522 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 08:37:13.522 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 08:37:13 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 08:37:13.523 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 08:37:13.523 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 08:37:13.523 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 08:37:13.523 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 08:37:13.523 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 08:37:13.524 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 08:37:13.524 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 08:37:13.524 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 08:37:13 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 08:37:13.524 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:37:13.525 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:37:13.545 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 08:37:13.545 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 08:37:13.546 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 08:37:13.546 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 08:37:13.546 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 08:37:13.546 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 08:37:13.546 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 08:37:13.546 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 08:37:13 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 08:37:13.561 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 08:37:13.562 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 08:37:13.562 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 08:37:13.562 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 08:37:13.562 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 08:37:13.562 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 08:37:13.563 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 08:37:13.563 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 08:37:13 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 08:37:13.571 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 08:37:13.571 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 08:37:13.571 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 08:37:13.571 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 08:37:13.571 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 08:37:13.571 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 08:37:13.571 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 08:37:13.572 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 08:37:13 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 08:37:13.830 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:37:13.830 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:37:13.836 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:37:13.836 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:37:13.856 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:37:13.856 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:37:13.871 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:37:13.872 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:37:15.597 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:37:15.597 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:37:15.927 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:37:15.928 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:37:15.930 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:37:15.932 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:37:15.932 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:37:15.933 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:37:15.942 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:37:15.942 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:37:15.954 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 08:37:15.954 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 08:37:16.266 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:37:16.266 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:37:16.267 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:37:16.267 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:37:16.268 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:37:16.268 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:37:16.281 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 08:37:16.281 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 08:37:24.931 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 08:37:26.432 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 08:37:27.935 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 08:37:28.602 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 08:37:29.184 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 08:37:29.507 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 08:37:30.082 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 08:37:30.657 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 08:37:35.614 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 08:37:38.710 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 08:37:40.241 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 08:37:40.815 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 08:37:41.395 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 08:37:41.765 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 08:37:42.349 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 08:37:42.942 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
