2025-08-02 08:37:30.659 [INFO] [ExecutionEngine] 🔥 ExecutionEngine日志系统启动 - 使用统一日志配置
2025-08-02 08:37:30.659 [INFO] [ExecutionEngine] ================================================================================
2025-08-02 08:37:30.659 [INFO] [ExecutionEngine] 🚀 ExecutionEngine初始化开始
2025-08-02 08:37:30.659 [INFO] [ExecutionEngine] ================================================================================
2025-08-02 08:37:30.659 [INFO] [ExecutionEngine] ✅ 并行套利控制器集成完成
2025-08-02 08:37:30.660 [INFO] [ExecutionEngine]    📊 最大并行数量: 3
2025-08-02 08:37:30.660 [INFO] [ExecutionEngine]    🔒 完全向下兼容现有逻辑
2025-08-02 08:37:30.660 [INFO] [ExecutionEngine] ✅ ExecutionEngine初始化完成
2025-08-02 08:37:30.660 [INFO] [ExecutionEngine] 🚀 初始化ExecutionEngine...
2025-08-02 08:37:30.660 [INFO] [ExecutionEngine] 🚀 步骤1.1: 检查交易所实例...
2025-08-02 08:37:30.660 [INFO] [ExecutionEngine] ✅ 使用传递的交易所实例: ['gate', 'bybit', 'okx']
2025-08-02 08:37:30.660 [INFO] [ExecutionEngine] 🔍 gate交易所需要初始化...
2025-08-02 08:37:32.283 [INFO] [ExecutionEngine] ✅ gate交易所初始化完成
2025-08-02 08:37:32.284 [INFO] [ExecutionEngine] 🔍 bybit交易所需要初始化...
2025-08-02 08:37:33.979 [INFO] [ExecutionEngine] ✅ bybit交易所初始化完成
2025-08-02 08:37:33.980 [INFO] [ExecutionEngine] 🔍 okx交易所需要初始化...
2025-08-02 08:37:33.980 [INFO] [ExecutionEngine] ✅ okx交易所初始化完成
2025-08-02 08:37:33.980 [INFO] [ExecutionEngine] 🎯 步骤1.2: 交易所验证完成，可用: 3个
2025-08-02 08:37:33.980 [INFO] [ExecutionEngine] 🚀 步骤2.1: 开始初始化交易器...
2025-08-02 08:37:33.988 [INFO] [ExecutionEngine] 🔍 步骤2.2: 导入交易器模块完成
2025-08-02 08:37:33.988 [INFO] [ExecutionEngine] 🔍 步骤2.3.1: 为gate创建交易器...
2025-08-02 08:37:33.989 [INFO] [ExecutionEngine]    ✅ gate现货交易器创建完成
2025-08-02 08:37:33.989 [INFO] [ExecutionEngine]    ✅ gate期货交易器创建完成
2025-08-02 08:37:33.989 [INFO] [ExecutionEngine] 🔍 步骤2.3.2: 为bybit创建交易器...
2025-08-02 08:37:33.989 [INFO] [ExecutionEngine]    ✅ bybit现货交易器创建完成
2025-08-02 08:37:33.989 [INFO] [ExecutionEngine]    ✅ bybit期货交易器创建完成
2025-08-02 08:37:33.989 [INFO] [ExecutionEngine] 🔍 步骤2.3.3: 为okx创建交易器...
2025-08-02 08:37:33.989 [INFO] [ExecutionEngine]    ✅ okx现货交易器创建完成
2025-08-02 08:37:33.989 [INFO] [ExecutionEngine]    ✅ okx期货交易器创建完成
2025-08-02 08:37:33.989 [INFO] [ExecutionEngine] 🎯 步骤2.4: 交易器初始化完成，共创建: 6个交易器
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] 🚀 步骤3.1: 开始初始化统一管理器...
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] 🔍 步骤3.2: 获取统一开仓管理器...
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] ✅ 统一开仓管理器获取成功
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] 🔍 步骤3.3: 获取统一平仓管理器...
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] ✅ 统一平仓管理器获取成功
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] 🔍 步骤3.4: 获取交易规则预加载器...
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] ✅ 交易规则预加载器获取成功
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] 🔍 步骤3.5: 初始化保证金计算器...
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] ✅ 保证金计算器初始化成功
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] 🔍 步骤3.6: 检查开仓管理器初始化方法...
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] ℹ️ 开仓管理器无需初始化（已就绪）
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] 🔍 步骤3.7: 检查平仓管理器初始化方法...
2025-08-02 08:37:33.990 [INFO] [ExecutionEngine] ℹ️ 平仓管理器无需初始化（已就绪）
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🎯 步骤3.8: 统一管理器初始化完成
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🚀 步骤4.1: 开始初始化订单管理器...
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🔍 步骤4.2: 导入OrderManager模块完成
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🔍 步骤4.3: 创建OrderManager实例...
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🔍 现货交易器列表: ['gate', 'bybit', 'okx']
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🔍 期货交易器列表: ['gate', 'bybit', 'okx']
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🔍 检查现货交易器 gate: SpotTrader
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🔍 检查现货交易器 bybit: SpotTrader
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🔍 检查现货交易器 okx: SpotTrader
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🔍 检查期货交易器 gate: FuturesTrader
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🔍 检查期货交易器 bybit: FuturesTrader
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🔍 检查期货交易器 okx: FuturesTrader
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] 🔍 开始调用OrderManager构造函数...
2025-08-02 08:37:33.991 [INFO] [ExecutionEngine] ✅ OrderManager实例创建成功
2025-08-02 08:37:33.992 [INFO] [ExecutionEngine] 🎯 步骤4.4: 订单管理器初始化完成
2025-08-02 08:37:33.992 [INFO] [ExecutionEngine] 🚀 步骤5.1: 开始预加载交易规则...
2025-08-02 08:37:33.992 [INFO] [ExecutionEngine] 🔍 步骤5.2: 调用preload_all_trading_rules...
2025-08-02 08:37:42.944 [INFO] [ExecutionEngine] ✅ 步骤5.3: 交易规则预加载成功
2025-08-02 08:37:42.944 [INFO] [ExecutionEngine] 🎯 预加载统计:
2025-08-02 08:37:42.944 [INFO] [ExecutionEngine]    缓存规则数: 60
2025-08-02 08:37:42.944 [INFO] [ExecutionEngine]    成功加载: 120
2025-08-02 08:37:42.945 [INFO] [ExecutionEngine]    失败加载: 0
2025-08-02 08:37:42.945 [INFO] [ExecutionEngine]    预加载耗时: 24.6ms
2025-08-02 08:37:42.945 [INFO] [ExecutionEngine] 🎯 步骤5.4: 交易规则预加载阶段完成
2025-08-02 08:37:42.945 [INFO] [ExecutionEngine] ✅ ExecutionEngine初始化完成
