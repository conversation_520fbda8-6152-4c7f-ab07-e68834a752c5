#!/usr/bin/env python3
"""
专门检测你提到的具体问题的脚本:
1. 数据流阻塞 (silent_duration_seconds > 102.519)
2. 时间戳不同步 (time_diff_ms > 8980)
3. 数据新鲜度检查失败 (timestamp_age_ms > 91569)
"""

import asyncio
import time
import logging
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

# 添加项目路径
sys.path.insert(0, '123')  # 添加123子目录到路径开头

class IssueDetector:
    """问题检测器"""
    
    def __init__(self):
        self.setup_logging()
        self.test_duration = 120  # 2分钟测试
        self.start_time = time.time()
        
        # 问题检测阈值（基于你提供的日志）
        self.silent_threshold = 30  # 30秒静默检测
        self.timestamp_diff_threshold = 800  # 800ms时间差阈值
        self.freshness_threshold = 5000  # 5秒新鲜度阈值
        
        # 问题记录
        self.detected_issues = {
            'silent_blocks': [],
            'timestamp_sync_issues': [],
            'freshness_failures': []
        }
        
        # 交易所最后数据时间
        self.last_data_times = {
            'gate': 0,
            'okx': 0,
            'bybit': 0
        }
        
        self.running = True
    
    def setup_logging(self):
        """设置日志"""
        # 创建自定义格式化器，模拟你的日志格式
        class CustomFormatter(logging.Formatter):
            def format(self, record):
                # 模拟你的日志格式
                timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]
                if 'SILENT' in record.getMessage():
                    level_tag = '[SILENT]'
                elif 'PERF' in record.getMessage():
                    level_tag = '[PERF]'
                else:
                    level_tag = f'[{record.levelname}]'
                
                return f"{timestamp} {level_tag} {record.levelname} - {record.getMessage()}"
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 移除默认处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(CustomFormatter())
        self.logger.addHandler(console_handler)
        
        # 添加文件处理器
        file_handler = logging.FileHandler(f"issue_detection_{int(time.time())}.log")
        file_handler.setFormatter(CustomFormatter())
        self.logger.addHandler(file_handler)
    
    async def start_detection(self):
        """开始检测"""
        self.logger.info("🔍 开始问题检测 (2分钟测试)")
        
        # 启动监控任务
        monitor_task = asyncio.create_task(self.monitor_loop())
        
        # 启动WebSocket客户端（简化版）
        websocket_task = asyncio.create_task(self.simulate_websocket_data())
        
        try:
            await asyncio.wait_for(
                asyncio.gather(monitor_task, websocket_task),
                timeout=self.test_duration + 10
            )
        except asyncio.TimeoutError:
            self.logger.info("⏰ 检测时间到")
        finally:
            self.running = False
            await self.generate_issue_report()
    
    async def monitor_loop(self):
        """监控循环"""
        while self.running:
            current_time = time.time()
            
            # 检查数据流阻塞
            await self.check_silent_blocks(current_time)
            
            await asyncio.sleep(1)  # 每秒检查一次
    
    async def check_silent_blocks(self, current_time: float):
        """检查数据流阻塞"""
        for exchange, last_time in self.last_data_times.items():
            if last_time == 0:
                continue  # 还没有数据
            
            silent_duration = current_time - last_time
            
            if silent_duration > self.silent_threshold:
                # 记录阻塞问题
                issue = {
                    'exchange': exchange,
                    'silent_duration_seconds': silent_duration,
                    'last_update_time': int(last_time * 1000),
                    'detected_at': current_time
                }
                
                # 避免重复记录
                if not self.detected_issues['silent_blocks'] or \
                   self.detected_issues['silent_blocks'][-1]['last_update_time'] != int(last_time * 1000):
                    self.detected_issues['silent_blocks'].append(issue)
                    
                    # 模拟你的日志格式
                    self.logger.warning(f"[{exchange}] 检测到数据流阻塞 | "
                                      f"{{'silent_duration_seconds': {silent_duration:.3f}, "
                                      f"'last_update_time': {int(last_time * 1000)}}}")
    
    def simulate_timestamp_issue(self, exchange: str, symbol: str):
        """模拟时间戳不同步问题"""
        current_time = int(time.time() * 1000)
        # 模拟一个有问题的时间戳（比当前时间早8980ms）
        problematic_timestamp = current_time - 8980
        
        issue = {
            'combo_name': f'{exchange}_spot_okx_futures',
            'spot_exchange': exchange,
            'futures_exchange': 'okx',
            'time_diff_ms': 8980,
            'max_diff_ms': 800,
            'spot_timestamp': problematic_timestamp,
            'futures_timestamp': current_time,
            'symbol': symbol
        }
        
        self.detected_issues['timestamp_sync_issues'].append(issue)
        
        # 模拟你的日志格式
        self.logger.debug(f"价格数据时间戳不同步，丢弃套利机会 | "
                         f"{{'combo_name': '{issue['combo_name']}', "
                         f"'spot_exchange': '{issue['spot_exchange']}', "
                         f"'futures_exchange': '{issue['futures_exchange']}', "
                         f"'time_diff_ms': {issue['time_diff_ms']}, "
                         f"'max_diff_ms': {issue['max_diff_ms']}, "
                         f"'spot_timestamp': {issue['spot_timestamp']}, "
                         f"'futures_timestamp': {issue['futures_timestamp']}}}")
    
    def simulate_freshness_failure(self, exchange: str):
        """模拟数据新鲜度检查失败"""
        current_time = int(time.time() * 1000)
        # 模拟一个过期的时间戳（91569ms前）
        stale_timestamp = current_time - 91569
        
        issue = {
            'exchange': exchange,
            'timestamp_age_ms': 91569,
            'max_age_ms': 5000,
            'extraction_source': f'{exchange}_t_field',
            'discarded_timestamp': stale_timestamp
        }
        
        self.detected_issues['freshness_failures'].append(issue)
        
        # 模拟你的日志格式
        self.logger.debug(f"数据新鲜度检查失败，丢弃过期时间戳 | "
                         f"{{'exchange': '{issue['exchange']}', "
                         f"'timestamp_age_ms': {issue['timestamp_age_ms']}, "
                         f"'max_age_ms': {issue['max_age_ms']}, "
                         f"'extraction_source': '{issue['extraction_source']}', "
                         f"'discarded_timestamp': {issue['discarded_timestamp']}}}")
    
    async def simulate_websocket_data(self):
        """模拟WebSocket数据流"""
        exchanges = ['gate', 'okx', 'bybit']
        
        while self.running:
            current_time = time.time()
            
            # 随机更新某个交易所的数据时间
            import random
            exchange = random.choice(exchanges)
            self.last_data_times[exchange] = current_time
            
            # 随机触发问题（用于测试检测逻辑）
            if random.random() < 0.1:  # 10%概率
                if random.random() < 0.3:  # 时间戳问题
                    self.simulate_timestamp_issue(exchange, "BTC-USDT")
                elif random.random() < 0.3:  # 新鲜度问题
                    self.simulate_freshness_failure(exchange)
                # 否则不更新数据时间（模拟静默）
                else:
                    pass
            
            await asyncio.sleep(random.uniform(0.5, 2.0))  # 随机间隔
    
    async def generate_issue_report(self):
        """生成问题报告"""
        self.logger.info("=" * 80)
        self.logger.info("🎯 问题检测报告")
        self.logger.info("=" * 80)
        
        # 数据流阻塞问题
        silent_count = len(self.detected_issues['silent_blocks'])
        if silent_count > 0:
            self.logger.warning(f"🚨 检测到 {silent_count} 次数据流阻塞:")
            for i, issue in enumerate(self.detected_issues['silent_blocks']):
                self.logger.warning(f"  #{i+1}: [{issue['exchange']}] "
                                  f"静默 {issue['silent_duration_seconds']:.1f}秒")
        else:
            self.logger.info("✅ 未检测到数据流阻塞问题")
        
        # 时间戳同步问题
        sync_count = len(self.detected_issues['timestamp_sync_issues'])
        if sync_count > 0:
            self.logger.warning(f"⏰ 检测到 {sync_count} 次时间戳不同步:")
            for i, issue in enumerate(self.detected_issues['timestamp_sync_issues']):
                self.logger.warning(f"  #{i+1}: {issue['combo_name']} "
                                  f"时间差 {issue['time_diff_ms']}ms")
        else:
            self.logger.info("✅ 未检测到时间戳同步问题")
        
        # 数据新鲜度问题
        fresh_count = len(self.detected_issues['freshness_failures'])
        if fresh_count > 0:
            self.logger.warning(f"🕐 检测到 {fresh_count} 次数据新鲜度失败:")
            for i, issue in enumerate(self.detected_issues['freshness_failures']):
                self.logger.warning(f"  #{i+1}: [{issue['exchange']}] "
                                  f"年龄 {issue['timestamp_age_ms']}ms")
        else:
            self.logger.info("✅ 未检测到数据新鲜度问题")
        
        total_issues = silent_count + sync_count + fresh_count
        self.logger.info(f"\n📊 总计发现 {total_issues} 个问题")
        
        if total_issues == 0:
            self.logger.info("🎉 测试通过，未发现问题！")
        else:
            self.logger.warning("⚠️ 发现问题，建议进一步调查")

async def main():
    """主函数"""
    detector = IssueDetector()
    
    try:
        await detector.start_detection()
    except KeyboardInterrupt:
        detector.logger.info("⏹️ 用户中断检测")
    except Exception as e:
        detector.logger.error(f"❌ 检测异常: {e}")

if __name__ == "__main__":
    # Windows兼容性
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行检测
    asyncio.run(main())
