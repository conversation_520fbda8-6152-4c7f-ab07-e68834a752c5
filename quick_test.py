#!/usr/bin/env python3
"""
快速WebSocket测试脚本
直接运行最重要的测试，无需菜单选择
"""

import asyncio
import time
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '123')  # 添加123子目录到路径开头

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    return logging.getLogger(__name__)

async def quick_websocket_test():
    """快速WebSocket测试"""
    logger = setup_logging()
    
    logger.info("🚀 开始快速WebSocket测试 (2分钟)")
    logger.info("=" * 60)
    
    start_time = time.time()
    test_duration = 120  # 2分钟
    
    # 统计数据
    stats = {
        'gate': {'messages': 0, 'errors': 0, 'last_message': 0},
        'okx': {'messages': 0, 'errors': 0, 'last_message': 0},
        'bybit': {'messages': 0, 'errors': 0, 'last_message': 0}
    }
    
    # 问题检测
    issues_detected = []
    
    async def test_exchange(exchange_name: str):
        """测试单个交易所"""
        try:
            logger.info(f"[{exchange_name.upper()}] 🔌 正在连接...")
            
            # 动态导入客户端
            if exchange_name == 'gate':
                from websocket.gate_ws import GateWebSocketClient
                client = GateWebSocketClient("spot")
                symbols = ["BTC-USDT", "ETH-USDT"]
            elif exchange_name == 'okx':
                from websocket.okx_ws import OKXWebSocketClient
                client = OKXWebSocketClient("spot")
                symbols = ["BTC-USDT", "ETH-USDT"]
            elif exchange_name == 'bybit':
                from websocket.bybit_ws import BybitWebSocketClient
                client = BybitWebSocketClient("spot")
                symbols = ["BTCUSDT", "ETHUSDT"]
            
            client.set_symbols(symbols)
            
            # 消息回调
            def on_message(data):
                current_time = time.time()
                stats[exchange_name]['messages'] += 1
                stats[exchange_name]['last_message'] = current_time
                
                # 每20条消息报告一次
                if stats[exchange_name]['messages'] % 20 == 0:
                    logger.info(f"[{exchange_name.upper()}] 📨 收到 {stats[exchange_name]['messages']} 条消息")
            
            # 错误回调
            def on_error(error):
                stats[exchange_name]['errors'] += 1
                logger.error(f"[{exchange_name.upper()}] ❌ 错误: {error}")
            
            client.register_callback("market_data", on_message)
            client.register_callback("orderbook", on_message)
            client.register_callback("error", on_error)
            
            # 运行客户端
            await asyncio.wait_for(client.run(), timeout=test_duration)
            
        except asyncio.TimeoutError:
            logger.info(f"[{exchange_name.upper()}] ⏰ 测试完成")
        except Exception as e:
            logger.error(f"[{exchange_name.upper()}] ❌ 异常: {e}")
            stats[exchange_name]['errors'] += 1
    
    async def monitor_issues():
        """监控问题"""
        while True:
            current_time = time.time()
            
            # 检查是否超时
            if current_time - start_time >= test_duration:
                break
            
            # 检查数据流阻塞
            for exchange, stat in stats.items():
                if stat['last_message'] > 0:
                    silent_duration = current_time - stat['last_message']
                    if silent_duration > 30:  # 30秒无数据
                        issue = f"[{exchange.upper()}] 数据流阻塞 {silent_duration:.1f}秒"
                        if issue not in issues_detected:
                            issues_detected.append(issue)
                            logger.warning(f"🚨 {issue}")
            
            await asyncio.sleep(5)  # 每5秒检查一次
    
    try:
        # 初始化时间戳处理器
        try:
            from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
            sync_results = await initialize_all_timestamp_processors(force_sync=True)
            logger.info(f"⏰ 时间同步: {sync_results}")
        except Exception as e:
            logger.warning(f"⚠️ 时间同步失败: {e}")
        
        # 启动所有测试
        tasks = [
            asyncio.create_task(test_exchange('gate')),
            asyncio.create_task(test_exchange('okx')),
            asyncio.create_task(test_exchange('bybit')),
            asyncio.create_task(monitor_issues())
        ]
        
        # 等待完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
    
    # 生成报告
    logger.info("=" * 60)
    logger.info("📊 测试结果")
    logger.info("=" * 60)
    
    total_messages = 0
    total_errors = 0
    
    for exchange, stat in stats.items():
        status = "✅ 正常" if stat['messages'] > 0 else "❌ 异常"
        logger.info(f"[{exchange.upper()}] {status} | "
                   f"消息: {stat['messages']} | 错误: {stat['errors']}")
        total_messages += stat['messages']
        total_errors += stat['errors']
    
    logger.info("-" * 60)
    logger.info(f"总计: 消息 {total_messages} | 错误 {total_errors} | 问题 {len(issues_detected)}")
    
    if issues_detected:
        logger.warning("🚨 发现的问题:")
        for issue in issues_detected:
            logger.warning(f"  - {issue}")
    
    if total_messages > 0 and total_errors == 0 and len(issues_detected) == 0:
        logger.info("🎉 所有测试通过！")
    elif total_messages > 0:
        logger.warning("⚠️ 测试完成，但发现问题")
    else:
        logger.error("❌ 测试失败")

async def main():
    """主函数"""
    try:
        await quick_websocket_test()
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    # Windows兼容性
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行测试
    asyncio.run(main())
