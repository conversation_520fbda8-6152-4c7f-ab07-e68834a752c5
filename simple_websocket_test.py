#!/usr/bin/env python3
"""
简化版WebSocket测试脚本
快速验证三交易所连接和基本功能
"""

import asyncio
import time
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '123')  # 添加123子目录到路径开头

class SimpleWebSocketTest:
    """简化WebSocket测试"""
    
    def __init__(self):
        self.setup_logging()
        self.test_duration = 120  # 2分钟
        self.start_time = time.time()
        
        # 统计数据
        self.stats = {
            'gate': {'messages': 0, 'errors': 0, 'connected': False},
            'okx': {'messages': 0, 'errors': 0, 'connected': False},
            'bybit': {'messages': 0, 'errors': 0, 'connected': False}
        }
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        self.logger = logging.getLogger(__name__)
    
    async def test_single_exchange(self, exchange_name: str):
        """测试单个交易所"""
        try:
            self.logger.info(f"🚀 测试 {exchange_name.upper()} WebSocket...")
            
            # 动态导入对应的客户端
            if exchange_name == 'gate':
                from websocket.gate_ws import GateWebSocketClient
                client = GateWebSocketClient("spot")
                symbols = ["BTC-USDT", "ETH-USDT"]
            elif exchange_name == 'okx':
                from websocket.okx_ws import OKXWebSocketClient
                client = OKXWebSocketClient("spot")
                symbols = ["BTC-USDT", "ETH-USDT"]
            elif exchange_name == 'bybit':
                from websocket.bybit_ws import BybitWebSocketClient
                client = BybitWebSocketClient("spot")
                symbols = ["BTCUSDT", "ETHUSDT"]  # Bybit格式
            else:
                self.logger.error(f"❌ 不支持的交易所: {exchange_name}")
                return
            
            # 设置交易对
            client.set_symbols(symbols)
            
            # 注册回调
            def on_message(data):
                self.stats[exchange_name]['messages'] += 1
                if self.stats[exchange_name]['messages'] % 10 == 0:
                    self.logger.info(f"[{exchange_name.upper()}] 收到第 {self.stats[exchange_name]['messages']} 条消息")
            
            def on_error(error):
                self.stats[exchange_name]['errors'] += 1
                self.logger.error(f"[{exchange_name.upper()}] 错误: {error}")
            
            client.register_callback("market_data", on_message)
            client.register_callback("orderbook", on_message)
            client.register_callback("error", on_error)
            
            # 标记为已连接（简化处理）
            self.stats[exchange_name]['connected'] = True
            
            # 运行客户端（有超时限制）
            await asyncio.wait_for(client.run(), timeout=self.test_duration)
            
        except asyncio.TimeoutError:
            self.logger.info(f"[{exchange_name.upper()}] ⏰ 测试时间到")
        except Exception as e:
            self.logger.error(f"[{exchange_name.upper()}] ❌ 异常: {e}")
            self.stats[exchange_name]['errors'] += 1
    
    async def run_all_tests(self):
        """运行所有测试"""
        self.logger.info("🎯 开始三交易所WebSocket测试 (2分钟)")
        self.logger.info("=" * 60)
        
        # 初始化时间戳处理器
        try:
            from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
            sync_results = await initialize_all_timestamp_processors(force_sync=True)
            self.logger.info(f"⏰ 时间同步结果: {sync_results}")
        except Exception as e:
            self.logger.warning(f"⚠️ 时间同步失败: {e}")
        
        # 并发测试三个交易所
        tasks = []
        for exchange in ['gate', 'okx', 'bybit']:
            task = asyncio.create_task(self.test_single_exchange(exchange))
            tasks.append(task)
        
        # 等待所有测试完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        self.logger.info("=" * 60)
        self.logger.info("📊 测试报告")
        self.logger.info("=" * 60)
        
        total_messages = 0
        total_errors = 0
        
        for exchange, stats in self.stats.items():
            status = "✅ 正常" if stats['messages'] > 0 else "❌ 异常"
            self.logger.info(f"[{exchange.upper()}] {status} | "
                           f"消息: {stats['messages']} | "
                           f"错误: {stats['errors']}")
            
            total_messages += stats['messages']
            total_errors += stats['errors']
        
        self.logger.info("-" * 60)
        self.logger.info(f"总计: 消息 {total_messages} | 错误 {total_errors}")
        
        # 判断测试结果
        if total_messages > 0 and total_errors == 0:
            self.logger.info("🎉 所有测试通过！")
        elif total_messages > 0:
            self.logger.warning(f"⚠️ 测试完成，但有 {total_errors} 个错误")
        else:
            self.logger.error("❌ 测试失败，没有收到任何消息")

async def main():
    """主函数"""
    test = SimpleWebSocketTest()
    
    try:
        await test.run_all_tests()
    except KeyboardInterrupt:
        test.logger.info("⏹️ 用户中断测试")
    except Exception as e:
        test.logger.error(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    # Windows兼容性
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行测试
    asyncio.run(main())
