#!/usr/bin/env python3
"""
WebSocket测试运行器
提供多种测试选项
"""

import sys
import os
import subprocess
import asyncio

def print_menu():
    """打印菜单"""
    print("=" * 60)
    print("🚀 WebSocket测试工具")
    print("=" * 60)
    print("1. 完整调试测试 (debug_websocket_test.py)")
    print("   - 详细监控三交易所WebSocket")
    print("   - 检测数据流阻塞、时间戳同步等问题")
    print("   - 生成详细报告和JSON文件")
    print()
    print("2. 简化连接测试 (simple_websocket_test.py)")
    print("   - 快速验证三交易所基本连接")
    print("   - 统计消息数量和错误")
    print("   - 适合快速检查")
    print()
    print("3. 问题检测器 (issue_detector.py)")
    print("   - 专门检测你提到的具体问题")
    print("   - 模拟问题场景进行测试")
    print("   - 验证检测逻辑")
    print()
    print("4. 查看项目结构")
    print("5. 退出")
    print("=" * 60)

def run_python_script(script_name):
    """运行Python脚本"""
    try:
        print(f"🚀 运行 {script_name}...")
        print("-" * 40)
        
        # 使用python3运行脚本
        result = subprocess.run([
            sys.executable, script_name
        ], cwd=os.getcwd(), capture_output=False, text=True)
        
        print("-" * 40)
        if result.returncode == 0:
            print(f"✅ {script_name} 运行完成")
        else:
            print(f"❌ {script_name} 运行失败 (退出码: {result.returncode})")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断 {script_name}")
    except Exception as e:
        print(f"❌ 运行 {script_name} 时出错: {e}")

def show_project_structure():
    """显示项目结构"""
    print("📁 项目结构:")
    print("├── debug_websocket_test.py     # 完整调试测试")
    print("├── simple_websocket_test.py    # 简化连接测试")
    print("├── issue_detector.py           # 问题检测器")
    print("├── run_tests.py               # 测试运行器 (当前文件)")
    print("└── websocket/                 # WebSocket模块")
    print("    ├── gate_ws.py             # Gate.io客户端")
    print("    ├── okx_ws.py              # OKX客户端")
    print("    ├── bybit_ws.py            # Bybit客户端")
    print("    └── unified_timestamp_processor.py  # 统一时间戳处理")
    print()

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_files = [
        "debug_websocket_test.py",
        "simple_websocket_test.py", 
        "issue_detector.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    # 检查websocket模块
    if not os.path.exists("websocket"):
        print("❌ 缺少websocket模块目录")
        return False
    
    print("✅ 所有文件就绪")
    return True

def main():
    """主函数"""
    print("🎯 三交易所WebSocket测试工具")
    print(f"📍 当前目录: {os.getcwd()}")
    print()
    
    # 检查依赖
    if not check_dependencies():
        print("请确保所有必要文件都存在")
        return
    
    while True:
        print_menu()
        
        try:
            choice = input("请选择 (1-5): ").strip()
            
            if choice == '1':
                run_python_script("debug_websocket_test.py")
            elif choice == '2':
                run_python_script("simple_websocket_test.py")
            elif choice == '3':
                run_python_script("issue_detector.py")
            elif choice == '4':
                show_project_structure()
            elif choice == '5':
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请输入1-5")
            
            if choice in ['1', '2', '3']:
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
