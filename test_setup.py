#!/usr/bin/env python3
"""
测试环境验证脚本
验证所有依赖和模块是否正确安装
"""

import sys
import os
import importlib

def test_python_version():
    """测试Python版本"""
    print("🐍 Python版本检查...")
    version = sys.version_info
    print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 7:
        print("   ✅ Python版本符合要求 (3.7+)")
        return True
    else:
        print("   ❌ Python版本过低，需要3.7+")
        return False

def test_project_structure():
    """测试项目结构"""
    print("\n📁 项目结构检查...")
    
    required_dirs = ['123', '123/websocket']
    required_files = [
        '123/websocket/__init__.py',
        '123/websocket/gate_ws.py',
        '123/websocket/okx_ws.py', 
        '123/websocket/bybit_ws.py',
        '123/websocket/unified_timestamp_processor.py'
    ]
    
    all_good = True
    
    # 检查目录
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"   ✅ 目录存在: {dir_path}")
        else:
            print(f"   ❌ 目录缺失: {dir_path}")
            all_good = False
    
    # 检查文件
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ 文件存在: {file_path}")
        else:
            print(f"   ❌ 文件缺失: {file_path}")
            all_good = False
    
    return all_good

def test_module_imports():
    """测试模块导入"""
    print("\n📦 模块导入检查...")
    
    # 添加路径
    sys.path.insert(0, '123')
    
    modules_to_test = [
        ('websocket.gate_ws', 'GateWebSocketClient'),
        ('websocket.okx_ws', 'OKXWebSocketClient'),
        ('websocket.bybit_ws', 'BybitWebSocketClient'),
        ('websocket.unified_timestamp_processor', 'initialize_all_timestamp_processors'),
        ('websocket.ws_client', 'WebSocketClient'),
    ]
    
    all_good = True
    
    for module_name, class_name in modules_to_test:
        try:
            module = importlib.import_module(module_name)
            if hasattr(module, class_name):
                print(f"   ✅ {module_name}.{class_name}")
            else:
                print(f"   ⚠️ {module_name} 导入成功，但缺少 {class_name}")
                all_good = False
        except ImportError as e:
            print(f"   ❌ {module_name} 导入失败: {e}")
            all_good = False
    
    return all_good

def test_script_files():
    """测试脚本文件"""
    print("\n📄 测试脚本检查...")
    
    script_files = [
        'quick_test.py',
        'debug_websocket_test.py',
        'simple_websocket_test.py',
        'issue_detector.py',
        'run_tests.py'
    ]
    
    all_good = True
    
    for script in script_files:
        if os.path.exists(script):
            print(f"   ✅ {script}")
        else:
            print(f"   ❌ {script} 缺失")
            all_good = False
    
    return all_good

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 基本功能检查...")
    
    try:
        sys.path.insert(0, '123')
        
        # 测试创建WebSocket客户端
        from websocket.gate_ws import GateWebSocketClient
        gate_client = GateWebSocketClient("spot")
        print("   ✅ Gate.io客户端创建成功")
        
        from websocket.okx_ws import OKXWebSocketClient
        okx_client = OKXWebSocketClient("spot")
        print("   ✅ OKX客户端创建成功")
        
        from websocket.bybit_ws import BybitWebSocketClient
        bybit_client = BybitWebSocketClient("spot")
        print("   ✅ Bybit客户端创建成功")
        
        # 测试设置交易对
        gate_client.set_symbols(["BTC-USDT"])
        okx_client.set_symbols(["BTC-USDT"])
        bybit_client.set_symbols(["BTCUSDT"])
        print("   ✅ 交易对设置成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 基本功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 WebSocket测试环境验证")
    print("=" * 50)
    
    tests = [
        ("Python版本", test_python_version),
        ("项目结构", test_project_structure),
        ("模块导入", test_module_imports),
        ("脚本文件", test_script_files),
        ("基本功能", test_basic_functionality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！环境配置正确，可以开始WebSocket测试")
        print("\n🚀 推荐运行命令:")
        print("   python3 quick_test.py        # 快速测试")
        print("   python3 run_tests.py         # 菜单选择")
        print("   ./run_test.sh               # Shell脚本")
    else:
        print(f"\n⚠️ 有 {total - passed} 项测试失败，请检查环境配置")
        print("\n🔧 可能的解决方案:")
        print("   1. 确保在正确的目录运行脚本")
        print("   2. 检查123/websocket目录是否存在")
        print("   3. 确保Python版本为3.7+")
        print("   4. 检查所有必要文件是否存在")

if __name__ == "__main__":
    main()
