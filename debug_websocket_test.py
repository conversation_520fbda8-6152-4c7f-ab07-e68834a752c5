#!/usr/bin/env python3
"""
三交易所WebSocket时间戳和连接稳定性调试脚本
测试时长：2分钟
检测问题：
1. 数据流阻塞 (silent_duration_seconds > 30)
2. 时间戳不同步 (time_diff_ms > 800)
3. 数据新鲜度检查失败 (timestamp_age_ms > 5000)
"""

import asyncio
import time
import json
import logging
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
import traceback

# 添加项目路径
sys.path.insert(0, '123')  # 添加123子目录到路径开头

@dataclass
class ExchangeStats:
    """交易所统计数据"""
    name: str
    connected: bool = False
    last_message_time: float = 0
    message_count: int = 0
    orderbook_count: int = 0
    error_count: int = 0
    silent_periods: List[Dict] = field(default_factory=list)
    timestamp_issues: List[Dict] = field(default_factory=list)
    freshness_failures: List[Dict] = field(default_factory=list)
    connection_events: List[Dict] = field(default_factory=list)

class WebSocketDebugger:
    """WebSocket调试器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.start_time = time.time()
        self.test_duration = 120  # 2分钟测试
        self.running = False
        
        # 交易所统计
        self.stats = {
            'gate': ExchangeStats('gate'),
            'okx': ExchangeStats('okx'), 
            'bybit': ExchangeStats('bybit')
        }
        
        # 监控阈值
        self.silent_threshold = 30  # 30秒无数据认为阻塞
        self.timestamp_sync_threshold = 800  # 800ms时间差阈值
        self.freshness_threshold = 5000  # 5秒新鲜度阈值
        
        # WebSocket客户端
        self.clients = {}
        
        # 测试交易对
        self.test_symbols = ["BTC-USDT", "ETH-USDT"]
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger("websocket_debug")
        logger.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # 文件处理器
        file_handler = logging.FileHandler(f"debug_websocket_{int(time.time())}.log")
        file_handler.setLevel(logging.DEBUG)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        
        logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        
        return logger
    
    async def initialize_clients(self):
        """初始化WebSocket客户端"""
        try:
            self.logger.info("🚀 初始化三交易所WebSocket客户端...")
            
            # 初始化时间戳处理器
            from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
            sync_results = await initialize_all_timestamp_processors(force_sync=True)
            self.logger.info(f"时间同步结果: {sync_results}")
            
            # 创建客户端
            from websocket.gate_ws import GateWebSocketClient
            from websocket.okx_ws import OKXWebSocketClient  
            from websocket.bybit_ws import BybitWebSocketClient
            
            # Gate.io客户端
            gate_client = GateWebSocketClient("spot")
            gate_client.set_symbols(self.test_symbols)
            self._register_callbacks(gate_client, 'gate')
            self.clients['gate'] = gate_client
            
            # OKX客户端
            okx_client = OKXWebSocketClient("spot")
            okx_client.set_symbols(self.test_symbols)
            self._register_callbacks(okx_client, 'okx')
            self.clients['okx'] = okx_client
            
            # Bybit客户端
            bybit_client = BybitWebSocketClient("spot")
            bybit_client.set_symbols([s.replace('-', '') for s in self.test_symbols])  # Bybit格式
            self._register_callbacks(bybit_client, 'bybit')
            self.clients['bybit'] = bybit_client
            
            self.logger.info("✅ WebSocket客户端初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 客户端初始化失败: {e}")
            self.logger.error(traceback.format_exc())
            raise
    
    def _register_callbacks(self, client, exchange_name: str):
        """注册回调函数"""
        
        async def on_market_data(data):
            """市场数据回调"""
            await self._handle_market_data(exchange_name, data)
        
        async def on_error(error_data):
            """错误回调"""
            await self._handle_error(exchange_name, error_data)
        
        async def on_connect():
            """连接回调"""
            await self._handle_connect(exchange_name)
        
        async def on_disconnect():
            """断开回调"""
            await self._handle_disconnect(exchange_name)
        
        # 注册回调
        client.register_callback("market_data", on_market_data)
        client.register_callback("orderbook", on_market_data)
        client.register_callback("error", on_error)
        client.register_callback("connect", on_connect)
        client.register_callback("disconnect", on_disconnect)
    
    async def _handle_market_data(self, exchange: str, data: Dict[str, Any]):
        """处理市场数据"""
        current_time = time.time()
        stats = self.stats[exchange]
        
        # 更新统计
        stats.message_count += 1
        stats.last_message_time = current_time
        
        if data.get('type') == 'orderbook':
            stats.orderbook_count += 1
        
        # 检查时间戳问题
        await self._check_timestamp_issues(exchange, data, current_time)
        
        # 详细日志（每10条消息记录一次）
        if stats.message_count % 10 == 0:
            self.logger.debug(f"[{exchange.upper()}] 收到第{stats.message_count}条消息: {data.get('symbol', 'N/A')}")
    
    async def _check_timestamp_issues(self, exchange: str, data: Dict[str, Any], current_time: float):
        """检查时间戳相关问题"""
        try:
            stats = self.stats[exchange]
            
            # 获取数据时间戳
            data_timestamp = data.get('timestamp', 0)
            if not data_timestamp:
                return
            
            # 转换为毫秒
            if data_timestamp < 1e12:
                data_timestamp *= 1000
            
            current_time_ms = int(current_time * 1000)
            time_diff = abs(data_timestamp - current_time_ms)
            
            # 检查时间戳同步问题
            if time_diff > self.timestamp_sync_threshold:
                issue = {
                    'timestamp': current_time,
                    'data_timestamp': data_timestamp,
                    'current_timestamp': current_time_ms,
                    'time_diff_ms': time_diff,
                    'symbol': data.get('symbol', 'N/A'),
                    'threshold': self.timestamp_sync_threshold
                }
                stats.timestamp_issues.append(issue)
                
                self.logger.warning(f"[{exchange.upper()}] 时间戳不同步: {data.get('symbol', 'N/A')} "
                                  f"时间差={time_diff}ms > {self.timestamp_sync_threshold}ms")
            
            # 检查数据新鲜度
            if time_diff > self.freshness_threshold:
                issue = {
                    'timestamp': current_time,
                    'data_timestamp': data_timestamp,
                    'age_ms': time_diff,
                    'symbol': data.get('symbol', 'N/A'),
                    'threshold': self.freshness_threshold
                }
                stats.freshness_failures.append(issue)
                
                self.logger.warning(f"[{exchange.upper()}] 数据新鲜度检查失败: {data.get('symbol', 'N/A')} "
                                  f"年龄={time_diff}ms > {self.freshness_threshold}ms")
                
        except Exception as e:
            self.logger.error(f"时间戳检查异常 [{exchange}]: {e}")
    
    async def _handle_error(self, exchange: str, error_data: Dict[str, Any]):
        """处理错误"""
        stats = self.stats[exchange]
        stats.error_count += 1
        
        self.logger.error(f"[{exchange.upper()}] WebSocket错误: {error_data}")
    
    async def _handle_connect(self, exchange: str):
        """处理连接事件"""
        current_time = time.time()
        stats = self.stats[exchange]
        stats.connected = True
        
        event = {
            'timestamp': current_time,
            'event': 'connect'
        }
        stats.connection_events.append(event)
        
        self.logger.info(f"[{exchange.upper()}] ✅ WebSocket已连接")
    
    async def _handle_disconnect(self, exchange: str):
        """处理断开事件"""
        current_time = time.time()
        stats = self.stats[exchange]
        stats.connected = False

        event = {
            'timestamp': current_time,
            'event': 'disconnect'
        }
        stats.connection_events.append(event)

        self.logger.warning(f"[{exchange.upper()}] ❌ WebSocket已断开")

    async def start_monitoring(self):
        """开始监控"""
        self.running = True
        self.logger.info(f"🔍 开始2分钟WebSocket监控测试...")

        # 启动所有客户端
        client_tasks = []
        for exchange, client in self.clients.items():
            task = asyncio.create_task(client.run())
            client_tasks.append(task)
            self.logger.info(f"[{exchange.upper()}] 启动WebSocket客户端")

        # 启动监控任务
        monitor_task = asyncio.create_task(self._monitor_loop())

        try:
            # 等待测试完成或客户端异常
            await asyncio.wait_for(monitor_task, timeout=self.test_duration + 10)
        except asyncio.TimeoutError:
            self.logger.info("⏰ 测试时间到，停止监控")
        except Exception as e:
            self.logger.error(f"❌ 监控异常: {e}")
        finally:
            self.running = False

            # 停止所有客户端
            for task in client_tasks:
                task.cancel()

            # 等待任务清理
            await asyncio.gather(*client_tasks, return_exceptions=True)

    async def _monitor_loop(self):
        """监控循环"""
        last_report_time = time.time()
        report_interval = 30  # 每30秒报告一次

        while self.running and (time.time() - self.start_time) < self.test_duration:
            current_time = time.time()

            # 检查数据流阻塞
            await self._check_silent_periods(current_time)

            # 定期报告
            if current_time - last_report_time >= report_interval:
                await self._report_status(current_time)
                last_report_time = current_time

            await asyncio.sleep(1)  # 每秒检查一次

        # 最终报告
        await self._generate_final_report()

    async def _check_silent_periods(self, current_time: float):
        """检查静默期间"""
        for exchange, stats in self.stats.items():
            if stats.last_message_time == 0:
                continue  # 还没有收到过消息

            silent_duration = current_time - stats.last_message_time

            if silent_duration > self.silent_threshold:
                # 记录静默期间
                silent_period = {
                    'start_time': stats.last_message_time,
                    'current_time': current_time,
                    'duration_seconds': silent_duration,
                    'last_update_time': int(stats.last_message_time * 1000)
                }

                # 避免重复记录同一个静默期间
                if not stats.silent_periods or \
                   (stats.silent_periods[-1]['start_time'] != stats.last_message_time):
                    stats.silent_periods.append(silent_period)

                    self.logger.warning(f"[SILENT] WARNING - [{exchange}] 检测到数据流阻塞 | "
                                      f"{{'silent_duration_seconds': {silent_duration:.3f}, "
                                      f"'last_update_time': {int(stats.last_message_time * 1000)}}}")

    async def _report_status(self, current_time: float):
        """报告状态"""
        elapsed = current_time - self.start_time
        self.logger.info(f"📊 测试进度: {elapsed:.1f}s / {self.test_duration}s")

        for exchange, stats in self.stats.items():
            status = "🟢 连接" if stats.connected else "🔴 断开"
            last_msg = "从未" if stats.last_message_time == 0 else f"{current_time - stats.last_message_time:.1f}s前"

            self.logger.info(f"[{exchange.upper()}] {status} | "
                           f"消息: {stats.message_count} | "
                           f"订单簿: {stats.orderbook_count} | "
                           f"错误: {stats.error_count} | "
                           f"最后消息: {last_msg}")

    async def _generate_final_report(self):
        """生成最终报告"""
        self.logger.info("=" * 80)
        self.logger.info("🎯 最终测试报告")
        self.logger.info("=" * 80)

        total_issues = 0

        for exchange, stats in self.stats.items():
            self.logger.info(f"\n[{exchange.upper()}] 统计:")
            self.logger.info(f"  📨 总消息数: {stats.message_count}")
            self.logger.info(f"  📖 订单簿消息: {stats.orderbook_count}")
            self.logger.info(f"  ❌ 错误数: {stats.error_count}")
            self.logger.info(f"  🔌 连接事件: {len(stats.connection_events)}")

            # 数据流阻塞问题
            if stats.silent_periods:
                self.logger.warning(f"  🚨 数据流阻塞: {len(stats.silent_periods)} 次")
                for i, period in enumerate(stats.silent_periods):
                    self.logger.warning(f"    #{i+1}: {period['duration_seconds']:.1f}秒静默")
                total_issues += len(stats.silent_periods)
            else:
                self.logger.info(f"  ✅ 数据流阻塞: 0 次")

            # 时间戳同步问题
            if stats.timestamp_issues:
                self.logger.warning(f"  ⏰ 时间戳不同步: {len(stats.timestamp_issues)} 次")
                for i, issue in enumerate(stats.timestamp_issues[:3]):  # 只显示前3个
                    self.logger.warning(f"    #{i+1}: {issue['symbol']} 时间差={issue['time_diff_ms']:.0f}ms")
                total_issues += len(stats.timestamp_issues)
            else:
                self.logger.info(f"  ✅ 时间戳同步: 正常")

            # 数据新鲜度问题
            if stats.freshness_failures:
                self.logger.warning(f"  🕐 数据新鲜度失败: {len(stats.freshness_failures)} 次")
                for i, failure in enumerate(stats.freshness_failures[:3]):  # 只显示前3个
                    self.logger.warning(f"    #{i+1}: {failure['symbol']} 年龄={failure['age_ms']:.0f}ms")
                total_issues += len(stats.freshness_failures)
            else:
                self.logger.info(f"  ✅ 数据新鲜度: 正常")

        # 总结
        self.logger.info(f"\n🎯 测试总结:")
        self.logger.info(f"  测试时长: {self.test_duration}秒")
        self.logger.info(f"  发现问题: {total_issues} 个")

        if total_issues == 0:
            self.logger.info("  🎉 所有测试通过，WebSocket连接稳定！")
        else:
            self.logger.warning(f"  ⚠️ 发现 {total_issues} 个问题，需要进一步调查")

        # 保存详细报告到文件
        await self._save_detailed_report()

    async def _save_detailed_report(self):
        """保存详细报告到JSON文件"""
        report_data = {
            'test_info': {
                'start_time': self.start_time,
                'duration': self.test_duration,
                'test_symbols': self.test_symbols,
                'thresholds': {
                    'silent_threshold': self.silent_threshold,
                    'timestamp_sync_threshold': self.timestamp_sync_threshold,
                    'freshness_threshold': self.freshness_threshold
                }
            },
            'results': {}
        }

        for exchange, stats in self.stats.items():
            report_data['results'][exchange] = {
                'message_count': stats.message_count,
                'orderbook_count': stats.orderbook_count,
                'error_count': stats.error_count,
                'connected': stats.connected,
                'silent_periods': stats.silent_periods,
                'timestamp_issues': stats.timestamp_issues,
                'freshness_failures': stats.freshness_failures,
                'connection_events': stats.connection_events
            }

        filename = f"websocket_debug_report_{int(self.start_time)}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

        self.logger.info(f"📄 详细报告已保存: {filename}")

async def main():
    """主函数"""
    debugger = WebSocketDebugger()

    try:
        # 初始化客户端
        await debugger.initialize_clients()

        # 开始监控
        await debugger.start_monitoring()

    except KeyboardInterrupt:
        debugger.logger.info("⏹️ 用户中断测试")
    except Exception as e:
        debugger.logger.error(f"❌ 测试异常: {e}")
        debugger.logger.error(traceback.format_exc())
    finally:
        debugger.logger.info("🏁 测试结束")

if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

    # 运行测试
    asyncio.run(main())
