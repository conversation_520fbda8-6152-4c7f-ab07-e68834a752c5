@echo off
chcp 65001 >nul
echo 🚀 三交易所WebSocket测试工具
echo ================================

echo 📍 当前目录: %CD%
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请安装Python 3.7+
    pause
    exit /b 1
)

python --version
echo.

echo 🎯 选择测试类型:
echo 1. 快速测试 (推荐)
echo 2. 完整调试测试
echo 3. 简化连接测试
echo 4. 问题检测器
echo 5. 测试菜单
echo.

set /p choice="请选择 (1-5): "

if "%choice%"=="1" (
    echo 🚀 运行快速测试...
    python quick_test.py
) else if "%choice%"=="2" (
    echo 🚀 运行完整调试测试...
    python debug_websocket_test.py
) else if "%choice%"=="3" (
    echo 🚀 运行简化连接测试...
    python simple_websocket_test.py
) else if "%choice%"=="4" (
    echo 🚀 运行问题检测器...
    python issue_detector.py
) else if "%choice%"=="5" (
    echo 🚀 运行测试菜单...
    python run_tests.py
) else (
    echo ❌ 无效选择
)

echo.
echo 测试完成！
pause
